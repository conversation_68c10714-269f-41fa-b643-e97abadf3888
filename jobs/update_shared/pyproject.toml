[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poe]
include = "../../common/poe_shared_tasks.toml"

[tool.poe.tasks]
_test = "pytest -vvv --failed-first --random-order --color=yes"
hooks = { "shell" = "../../.hooks/install.sh 'jobs/update_shared'" }

[tool.poetry]
name = "update_shared"
version = "0.1.0"
description = "Job updating shared table on ADLS"
authors = ["IndieBI"]

[tool.poetry.dependencies]
python = "^3.12"
pandas = "2.2.2"
azure-identity = "^1.13.0"
azure-servicebus = "^7.7.0"
pydantic = "^2.6.3"
numpy = "1.26.4"
pandera = "^0.11.0"
azure-common = "^1.1.27"
azure-core = "^1.19.0"
azure-storage-blob = "^12.9.0"
azure-storage-file-datalake = "^12.8.0"
sentry-sdk = "^2.0.0"
pyarrow = "17.0.0"
pipeline-sdk = { version = "^2.4.0", source = "indiebi" }

[tool.poetry.group.dev.dependencies]
black = "^22.6.0"
mypy = "^1.0.0"
pylint = "^2.14.3"
types-requests = "^2.28.0"
ruff = "^0.4.5"
poethepoet = "^0.26.1"
pre-commit = "^3.7.1"
responses = "^0.25.3"

[tool.poetry.group.test.dependencies]
pytest = "^8.2.2"
pytest-deadfixtures = "^2.2.1"
pytest-xdist = "^3.6.1"
pytest-random-order = "^1.1.1"

[[tool.poetry.source]]
name = "indiebi"
url = "https://gitlab.com/api/v4/groups/4449864/-/packages/pypi/simple"
priority = "explicit"

[tool.ruff.lint.per-file-ignores]
"update_shared/main.py" = [
    "E402", # Module level import not at top of file
]
