[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poe]
include = "../../common/poe_shared_tasks.toml"

[tool.poe.tasks]
hooks = { "shell" = "../../.hooks/install.sh 'jobs/saas_gold'" }
_test = "pytest -vvv --failed-first --random-order --color=yes"

[tool.poetry]
name = "saas_gold"
version = "0.1.0"
description = ""
authors = ["Bart<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.ruff]
src = [".", "tests"]

[tool.ruff.lint]
select = [
    # Pyflakes
    "F",
    # Pycodestyle
    "E",
    "W",
    # isort
    "I",
]
ignore = ["E501", "E712"]


[tool.ruff.lint.isort]
known-first-party = ["saas_gold", "data_sdk"]

[tool.ruff.lint.per-file-ignores]
"saas_gold/main.py" = ["E402"]

[tool.poetry.dependencies]
python = "~3.12"
pandas = "^2.2.0"
pyarrow = "^17.0.0"
pydantic-settings = "^2.1.0"
pydantic = "^2.6.1"
polars = "^0.20.10"
pipeline-sdk = {version = "^2.5.1", source = "indiebi"}
sentry-sdk = "^2.0.0"
typer = { extras = ["all"], version = "^0.12.3" }
data-sdk = {version = "^1.5.2", source = "indiebi"}
pendulum = "^3.0.0"

[tool.poetry.group.dev.dependencies]
ruff = "^0.4.5"
mypy = "^1.8.0"
pandas-stubs = "^2.1.4.231227"
pytest-random-order = "^1.1.1"
poethepoet = "^0.26.1"
pre-commit = "^3.7.1"

[tool.poetry.group.test.dependencies]
pytest = "^8.2.2"
pytest-deadfixtures = "^2.2.1"
pytest-xdist = "^3.6.1"
pytest-random-order = "^1.1.1"
factory-boy = "^3.3.3"

[[tool.poetry.source]]
name = "indiebi"
url = "https://gitlab.com/api/v4/groups/4449864/-/packages/pypi/simple"
priority = "explicit"
