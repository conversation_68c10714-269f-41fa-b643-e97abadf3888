[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poe]
include = "../../common/poe_shared_tasks.toml"

[tool.poe.tasks]
hooks = { "shell" = "../../.hooks/install.sh 'jobs/find_shards'" }

[tool.poetry]
name = "find_shards"
version = "0.1.0"
description = ""
authors = ["Salvador <<EMAIL>>"]
packages = [{ include = "find_shards" }]

[tool.poetry.dependencies]
python = "~3.12"
pipeline-sdk = {version = "^2.5.1", source = "indiebi"}
pydantic = "^2.6.3"
azure-servicebus = "^7.8.1"
azure-identity = "^1.13.0"
sentry-sdk = "^2.0.0"

[tool.poetry.group.dev.dependencies]
black = "^22.10.0"
responses = "^0.25.0"
mypy = "^1.0.0"
ruff = "^0.4.5"
poethepoet = "^0.26.1"
pre-commit = "^3.7.1"
typer = "^0.16.0"

[tool.poetry.group.test.dependencies]
pytest = "^8.2.2"
pytest-deadfixtures = "^2.2.1"
pytest-xdist = "^3.6.1"
pytest-random-order = "^1.1.1"

[[tool.poetry.source]]
name = "indiebi"
url = "https://gitlab.com/api/v4/groups/4449864/-/packages/pypi/simple"
priority = "explicit"

[tool.ruff.lint.per-file-ignores]
"find_shards/main.py" = [
    "E402", # Module level import not at top of file
]
