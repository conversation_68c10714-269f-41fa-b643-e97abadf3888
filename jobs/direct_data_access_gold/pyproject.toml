[tool.poetry]
name = "direct-data-access-gold"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poe]
include = "../../common/poe_shared_tasks.toml"

[tool.ruff]
src = [".", "tests"]

[tool.ruff.lint]
select = [
    # Pyflakes
    "F",
    # Pycodestyle
    "E",
    "W",
    # isort
    "I",
]
ignore = ["E501", "E712"]


[tool.ruff.lint.isort]
known-first-party = ["direct_data_access_gold"]

[tool.ruff.lint.per-file-ignores]
"direct_data_access_gold/main.py" = ["E402"]

[tool.poetry.dependencies]
python = "~3.12"
pydantic = "^2.6.3"
sentry-sdk = "^2.0.0"
pipeline-sdk = { version = "^2.4.0", source = "indiebi" }
data-sdk = { version = "1.5.0", source = "indiebi" }

[tool.poetry.group.dev.dependencies]
debugpy = "^1.8.1"
factory-boy = "^3.3.0"
ruff = "^0.4.5"
pre-commit = "^3.7.1"
poethepoet = "^0.26.1"

[tool.poetry.group.test.dependencies]
pytest = "^8.2.2"
pytest-deadfixtures = "^2.2.1"
pytest-xdist = "^3.6.1"
pytest-random-order = "^1.1.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"


[[tool.poetry.source]]
name = "indiebi"
url = "https://gitlab.com/api/v4/groups/4449864/-/packages/pypi/simple"
priority = "explicit"
