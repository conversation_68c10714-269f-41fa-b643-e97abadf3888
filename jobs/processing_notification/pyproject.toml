
[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poe]
include = "../../common/poe_shared_tasks.toml"

[tool.poe.tasks]
_test = "pytest -vvv --random-order --color=yes"
hooks = { "shell" = "../../.hooks/install.sh 'jobs/processing_notification'" }

[tool.isort]
profile = "black"

[tool.poetry]
name = "processing-notification"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "~3.12"
pipeline-sdk = {version = "^2.5.1", source = "indiebi"}
sentry-sdk = "^2.0.0"
pydantic = "^2.6.3"
azure-identity = "^1.15.0"
onesignal-python-api = "^2.0.2"

[tool.poetry.group.dev.dependencies]
black = "^24.2.0"
mypy = "^1.8.0"
pylint = "^3.1.0"
pytest = "^8.0.2"
responses = "^0.25.0"
pytest-deadfixtures = "^2.2.1"
ruff = "^0.4.5"
freezegun = "^1.4.0"
pytest-random-order = "^1.1.1"
poethepoet = "^0.26.1"
pre-commit = "^3.7.1"
pytest-xdist = "^3.6.1"


[tool.poetry.group.test.dependencies]
pytest = "^8.2.2"
pytest-deadfixtures = "^2.2.1"
pytest-xdist = "^3.6.1"
pytest-random-order = "^1.1.1"

[[tool.poetry.source]]
name = "indiebi"
url = "https://gitlab.com/api/v4/groups/4449864/-/packages/pypi/simple"
priority = "explicit"

[tool.ruff.lint.per-file-ignores]
"processing_notification/main.py" = [
    "E402", # Module level import not at top of file
]
