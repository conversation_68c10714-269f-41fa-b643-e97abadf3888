import datetime
import logging

from elasticapm import capture_span
from pydantic import BaseModel

from core_silver.aggregators import aggregator_list as silver_aggregator_list
from core_silver.config import Config
from core_silver.external_sources.connectors.report_service import ReportServiceClient
from core_silver.external_sources.connectors.user_service import UserServiceClient
from core_silver.external_sources.process import process_external_sources
from core_silver.legacy_aggregators import aggregator_list as legacy_aggregator_list
from core_silver.observation_converter.process_studio_files import process_studio_files
from core_silver.reporters.sku_report_service_reporter import SkuReportServiceReporter
from core_silver.reporters.status_report_service_reporter import (
    StatusReportServiceReporter,
)
from data_sdk.aggregator import BaseAggregator, process_aggregators
from data_sdk.crawled_public_data.reader import CrawledPublicDataReader
from data_sdk.custom_partition.reader import CustomPartitionReader
from data_sdk.custom_partition.writer import CustomPartitionsWriter
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.observations import ObservationType
from data_sdk.reporter import <PERSON><PERSON><PERSON><PERSON><PERSON>, process_reporters
from data_sdk.reports.reader import Con<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RawReportsReader
from data_sdk.reports.writer import ConvertedReportsWriter

log = logging.getLogger(__name__)


class JobInputParameters(BaseModel):
    studio_id: StudioId
    observation_type: ObservationType
    portal: Portal


def run(params: JobInputParameters, config: Config) -> None:
    creation_datetime = datetime.datetime.now(datetime.UTC)

    # per studio_id
    top_level_process_external_sources(
        config=config,
        studio_id=params.studio_id,
        creation_datetime=creation_datetime,
    )

    # per studio_id, portal, observation type
    top_level_observation_converter(
        config=config,
        studio_id=params.studio_id,
        portal=params.portal,
        observation_type=params.observation_type,
        creation_datetime=creation_datetime,
    )

    # per studio, portal
    top_level_process_aggregators(
        aggregators=silver_aggregator_list,
        config=config,
        studio_id=params.studio_id,
        portal=params.portal,
        creation_datetime=creation_datetime,
    )

    top_level_process_aggregators(
        aggregators=legacy_aggregator_list,
        config=config,
        studio_id=params.studio_id,
        portal=params.portal,
        creation_datetime=creation_datetime,
    )

    # TODO implements top_level_update_states
    top_level_process_reporters(
        config=config,
        studio_id=params.studio_id,
        portal=params.portal,
        observation_type=params.observation_type,
        creation_datetime=creation_datetime,
    )


@capture_span()
def top_level_observation_converter(
    config: Config,
    studio_id: StudioId,
    portal: Portal,
    observation_type: ObservationType,
    creation_datetime: datetime.datetime,
) -> None:
    log.info(
        "Observation converter for studio_id: %s, portal: %s, observation_type: %s",
        studio_id,
        portal,
        observation_type,
    )

    raw_reader = RawReportsReader.get_reader(config.input_cfg)
    converted_reader = ConvertedReportsReader.get_reader(config.converted_reports_cfg)
    converted_writer = ConvertedReportsWriter.get_writer(config.converted_reports_cfg)

    from data_sdk.config import DLSConfig

    result_reader = CustomPartitionReader.get_reader(
        DLSConfig(
            account_name="dlsaggregatedprodr9",
            container_name="core-silver",
            base_dir="result",
        )
    )
    result_writer = CustomPartitionsWriter.get_writer(config.output_cfg)

    process_studio_files(
        studio_id=studio_id,
        portal=portal,
        observation_type=observation_type,
        creation_datetime=creation_datetime,
        raw_reader=raw_reader,
        converted_reader=converted_reader,
        converted_writer=converted_writer,
        result_reader=result_reader,
        result_writer=result_writer,
    )


@capture_span()
def top_level_process_external_sources(
    config: Config,
    studio_id: StudioId,
    creation_datetime: datetime.datetime,
) -> None:
    log.info("External sources for studio_id: %s", studio_id)

    result_writer = CustomPartitionsWriter.get_writer(config.output_cfg)
    report_service_client = ReportServiceClient.get_client(config.report_service)
    user_service_client = UserServiceClient.get_client(config.user_service)
    reader = CrawledPublicDataReader.get_reader(config.crawled_public_data_cfg)

    # process_external_sources(
    #     studio_id=studio_id,
    #     report_service_client=report_service_client,
    #     user_service_client=user_service_client,
    #     writer=result_writer,
    #     crawled_public_data_reader=reader,
    #     creation_datetime=creation_datetime,
    # )


@capture_span()
def top_level_process_aggregators(
    aggregators: list[type[BaseAggregator]],
    config: Config,
    studio_id: StudioId,
    portal: Portal,
    creation_datetime: datetime.datetime,
) -> None:
    log.info(
        "Aggregators for studio_id: %s, and aggregators_count: %s",
        studio_id,
        len(aggregators),
    )

    result_writer = CustomPartitionsWriter.get_writer(config.output_cfg)
    result_reader = CustomPartitionReader.get_reader(config.output_cfg)

    process_aggregators(
        aggregators=aggregators,
        reader=result_reader,
        writer=result_writer,
        creation_datetime=creation_datetime,
        studio_id=studio_id,
        portal=portal,
    )


@capture_span()
def top_level_process_reporters(
    config: Config,
    studio_id: StudioId,
    portal: Portal,
    observation_type: ObservationType,
    creation_datetime: datetime.datetime,
) -> None:
    log.info("Reporters for studio_id: %s", studio_id)

    reporters_class_list: list[type[BaseReporter]] = [
        SkuReportServiceReporter,
        StatusReportServiceReporter,
    ]

    report_service_client = ReportServiceClient.get_client(config.report_service)
    result_reader = CustomPartitionReader.get_reader(config.output_cfg)

    process_reporters(
        reporters=reporters_class_list,
        reader=result_reader,
        dependencies={ReportServiceClient: report_service_client},
        studio_id=studio_id,
        portal=portal,
        observation_type=observation_type,
    )
