#!/bin/bash

# Default values
JOBS_DIR="jobs"
SDKS=("pipeline-sdk" "data-sdk")

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dir)
            JOBS_DIR="$2"
            shift 2
            ;;
        --sdks)
            IFS=',' read -ra SDKS <<< "$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [--dir DIRECTORY] [--sdks SDK1,SDK2,...]"
            echo "  --dir: Directory containing projects (default: jobs)"
            echo "  --sdks: Comma-separated list of SDKs to update (default: pipeline-sdk,data-sdk)"
            echo "Example: $0 --dir my-jobs --sdks pipeline-sdk"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Function to update SDK dependencies
update_sdks() {
    local project_dir="$1"
    local updated_any=false

    echo "Checking $project_dir..."

    # Check which SDKs are present in the project
    for sdk in "${SDKS[@]}"; do
        if grep -q "$sdk" "$project_dir/pyproject.toml"; then
            echo "  Found $sdk, updating..."
            cd "$project_dir"

            if poetry add --source indiebi "$sdk@latest"; then
                echo "  ✓ Successfully updated $sdk"
                updated_any=true
            else
                echo "  ✗ Failed to update $sdk"
            fi

            cd - > /dev/null
        fi
    done

    if [[ "$updated_any" == false ]]; then
        echo "  No target SDKs found in $project_dir"
    fi
}

# Iterate over each folder in the specified directory
for project in "$JOBS_DIR"/*; do
    if [[ -d "$project" && -f "$project/pyproject.toml" ]]; then
        update_sdks "$project"
    fi
done

echo "Update process completed."
