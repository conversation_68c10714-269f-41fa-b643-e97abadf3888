#!/bin/bash

# Default values
JOBS_DIR="jobs"
SDKS=("pipeline-sdk" "data-sdk")
UPDATE_PYTHON=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --sdks)
            IFS=',' read -ra SDKS <<< "$2"
            shift 2
            ;;
        --update-python)
            UPDATE_PYTHON=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--sdks SDK1,SDK2,...] [--update-python]"
            echo "  --sdks: Comma-separated list of SDKs to update (default: pipeline-sdk,data-sdk)"
            echo "  --update-python: Update Python environment based on pyproject.toml version"
            echo "Examples:"
            echo "  $0 --sdks pipeline-sdk"
            echo "  $0 --update-python"
            echo "  $0 --sdks pipeline-sdk --update-python"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Function to extract Python version from pyproject.toml
get_python_version() {
    local project_dir="$1"
    local pyproject_file="$project_dir/pyproject.toml"

    # Look for python version in requires-python or python dependency
    local python_version=$(grep -E '^python\s*=|requires-python\s*=' "$pyproject_file" | head -1 | sed -E 's/.*[">= ]+([0-9]+\.[0-9]+).*/\1/')

    if [[ -n "$python_version" ]]; then
        echo "$python_version"
    else
        echo ""
    fi
}

# Function to update Python environment
update_python_env() {
    local project_dir="$1"

    echo "Updating Python environment for $project_dir..."

    local python_version=$(get_python_version "$project_dir")

    if [[ -z "$python_version" ]]; then
        echo "  ⚠ No Python version found in pyproject.toml, skipping"
        return
    fi

    echo "  Found Python version: $python_version"

    cd "$project_dir"

    echo "  Setting poetry environment to python$python_version..."
    if poetry env use "python$python_version"; then
        echo "  ✓ Successfully set Python environment to $python_version"

        echo "  Installing dependencies..."
        if poetry install; then
            echo "  ✓ Successfully installed dependencies"
        else
            echo "  ✗ Failed to install dependencies"
        fi
    else
        echo "  ✗ Failed to set Python environment to $python_version"
    fi

    cd - > /dev/null
}

# Function to update SDK dependencies
update_sdks() {
    local project_dir="$1"
    local updated_any=false

    echo "Updating SDKs for $project_dir..."

    # Check which SDKs are present in the project
    for sdk in "${SDKS[@]}"; do
        if grep -q "$sdk" "$project_dir/pyproject.toml"; then
            echo "  Found $sdk, updating..."
            cd "$project_dir"

            if poetry add --source indiebi "$sdk@latest"; then
                echo "  ✓ Successfully updated $sdk"
                updated_any=true
            else
                echo "  ✗ Failed to update $sdk"
            fi

            cd - > /dev/null
        fi
    done

    if [[ "$updated_any" == false ]]; then
        echo "  No target SDKs found in $project_dir"
    fi
}

# Iterate over each folder in the specified directory
for project in "$JOBS_DIR"/*; do
    if [[ -d "$project" && -f "$project/pyproject.toml" ]]; then
        update_sdks "$project"
    fi
done

echo "Update process completed."
